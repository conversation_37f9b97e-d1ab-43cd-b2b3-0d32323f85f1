# Migration từ OpenAIEmbeddings sang sentence_transformers

## Tổng quan
Đã thành công chuyển đổi `libs/typesense_vector_db.py` từ sử dụng `OpenAIEmbeddings` sang `sentence_transformers` để tạo embeddings cục bộ, không cần API key.

## Các thay đổi chính

### 1. Import và Dependencies
**Trước:**
```python
from langchain_openai import OpenAIEmbeddings
```

**Sau:**
```python
from sentence_transformers import SentenceTransformer
```

**Thêm vào requirements.txt:**
```
sentence-transformers>=2.2.0
```

### 2. Khởi tạo Embeddings
**Trước:**
```python
self.embeddings = OpenAIEmbeddings(
    model=embeddings_model,
    openai_api_base=embeddings_api_base,
    openai_api_key=embeddings_api_key,
    dimensions=384
)
```

**Sau:**
```python
self.embeddings = SentenceTransformer(embeddings_model)

# Kiểm tra dimension và đảm bảo không vượt quá 384
test_embedding = self.embeddings.encode("test")
actual_dim = len(test_embedding)
self.embedding_dimension = min(actual_dim, 384)
```

### 3. Tạo Embedding
**Trước:**
```python
embedding = self.embeddings.embed_query(text)
```

**Sau:**
```python
def generate_embedding(self, text: str) -> List[float]:
    embedding = self.embeddings.encode(text).tolist()
    # Truncate to embedding_dimension if needed
    if len(embedding) > self.embedding_dimension:
        embedding = embedding[:self.embedding_dimension]
    return embedding
```

### 4. Cấu hình Environment
**Trước (.env.example):**
```env
EMBEDDINGS_MODEL=text-embedding-ada-002
EMBEDDINGS_API_BASE=https://api.openai.com/v1
EMBEDDINGS_API_KEY=your_openai_api_key
```

**Sau (.env.example):**
```env
# Sentence Transformers Embeddings (no API key needed)
EMBEDDINGS_MODEL=all-MiniLM-L6-v2
```

## Lợi ích của việc chuyển đổi

### 1. **Không cần API Key**
- Không cần kết nối internet
- Không có chi phí API calls
- Không có rate limits

### 2. **Hiệu suất**
- Chạy cục bộ trên GPU/CPU
- Không có network latency
- Batch processing hiệu quả

### 3. **Privacy & Security**
- Dữ liệu không rời khỏi máy local
- Không gửi text lên external services

### 4. **Tương thích**
- Vẫn giữ nguyên interface API
- Dimension 384 được duy trì
- Tương thích với Typesense schema

## Models được hỗ trợ

### Recommended Models (384 dimensions):
- `all-MiniLM-L6-v2` (default) - Cân bằng tốt giữa chất lượng và tốc độ
- `all-MiniLM-L12-v2` - Chất lượng cao hơn, chậm hơn
- `paraphrase-MiniLM-L6-v2` - Tốt cho paraphrase detection

### Multilingual Models:
- `paraphrase-multilingual-MiniLM-L12-v2` - Hỗ trợ nhiều ngôn ngữ
- `distiluse-base-multilingual-cased` - Multilingual, 512 dimensions

## Testing

Chạy test để kiểm tra integration:
```bash
python test_sentence_transformers.py
```

Test bao gồm:
- ✅ Model initialization
- ✅ Embedding generation
- ✅ Dimension verification
- ✅ Similarity comparison
- ✅ Vietnamese text support

## Kết quả Test

```
📊 Model Information:
   Model name: all-MiniLM-L6-v2
   Model device: mps:0
   Max sequence length: 256
   Embedding dimension: 384

✅ All tests passed! sentence_transformers integration successful.
```

## Backward Compatibility

Các method public vẫn giữ nguyên interface:
- `import_docx_to_typesense()`
- `import_excel_to_typesense()`
- `search_similar_documents()`
- `search_and_answer()`

## Performance Notes

- **First run**: Model sẽ được download (~90MB cho all-MiniLM-L6-v2)
- **Subsequent runs**: Model được cache, khởi động nhanh
- **GPU acceleration**: Tự động sử dụng MPS (macOS) hoặc CUDA nếu có
- **Batch processing**: Hiệu quả cho multiple texts

## Migration Checklist

- [x] Cập nhật imports
- [x] Thay đổi embedding initialization
- [x] Cập nhật embedding generation methods
- [x] Thêm dimension handling
- [x] Cập nhật requirements.txt
- [x] Cập nhật documentation
- [x] Tạo test cases
- [x] Verify backward compatibility

## Next Steps

1. **Optional**: Cập nhật các file khác sử dụng OpenAIEmbeddings:
   - `libs/ai.py`
   - `extract_md_sections_ai.py`

2. **Performance tuning**: Có thể thử các models khác nhau tùy use case

3. **Monitoring**: Theo dõi performance và accuracy so với OpenAI embeddings
