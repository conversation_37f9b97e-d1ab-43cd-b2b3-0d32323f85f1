#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để demo API embedding với mock server
"""

import os
import json
import time
import threading
from http.server import HTTPServer, BaseHTTPRequestHandler
from libs.typesense_vector_db import TypesenseVectorDB

class MockEmbeddingAPIHandler(BaseHTTPRequestHandler):
    """Mock API server để test embedding API"""
    
    def do_POST(self):
        if self.path == '/embeddings':
            # Đọc request body
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                request_data = json.loads(post_data.decode('utf-8'))
                input_texts = request_data.get('input', [])
                
                # Tạo mock embeddings (384 dimensions với random values)
                import random
                embeddings = []
                for text in input_texts:
                    # Tạo embedding giả dựa trên hash của text để consistent
                    random.seed(hash(text) % 2**32)
                    embedding = [random.uniform(-1, 1) for _ in range(384)]
                    embeddings.append(embedding)
                
                # Response theo format OpenAI
                response = {
                    "data": [
                        {"embedding": emb} for emb in embeddings
                    ],
                    "model": "mock-embedding-model",
                    "usage": {
                        "prompt_tokens": sum(len(text.split()) for text in input_texts),
                        "total_tokens": sum(len(text.split()) for text in input_texts)
                    }
                }
                
                # Gửi response
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode('utf-8'))
                
            except Exception as e:
                # Error response
                self.send_response(400)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                error_response = {"error": str(e)}
                self.wfile.write(json.dumps(error_response).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()
    
    def log_message(self, format, *args):
        # Suppress default logging
        pass

def start_mock_server(port=8899):
    """Khởi động mock API server"""
    server = HTTPServer(('localhost', port), MockEmbeddingAPIHandler)
    print(f"🚀 Mock embedding API server started at http://localhost:{port}")
    server.serve_forever()

def test_api_embedding():
    """Test API embedding với mock server"""
    print("=" * 60)
    print("🌐 TESTING API EMBEDDING WITH MOCK SERVER")
    print("=" * 60)

    # Backup original environment variables
    original_url = os.environ.get('EMBEDDING_API_URL')
    original_key = os.environ.get('EMBEDDING_API_KEY')
    original_model = os.environ.get('EMBEDDING_API_MODEL')

    # Set environment variables cho mock API
    os.environ['EMBEDDING_API_URL'] = 'http://localhost:8899/embeddings'
    os.environ['EMBEDDING_API_KEY'] = 'mock_api_key'
    os.environ['EMBEDDING_API_MODEL'] = 'mock-embedding-model'
    
    try:
        # Khởi tạo với API embedding
        db = TypesenseVectorDB(
            collection_name="test_api_embedding",
            embedding_method="api"
        )
        
        print("\n📝 Testing single text embedding...")
        text = "Đây là test API embedding với mock server"
        embedding = db.generate_embedding(text)
        print(f"Text: {text}")
        print(f"Embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
        
        print("\n📝 Testing batch embedding...")
        texts = [
            "API embedding test 1",
            "API embedding test 2", 
            "API embedding test 3"
        ]
        embeddings = db.generate_embedding(texts, show_progress=True)
        print(f"Generated {len(embeddings)} embeddings")
        for i, emb in enumerate(embeddings):
            print(f"  Text {i+1}: dimension {len(emb)}")
        
        print("\n📊 Performance stats:")
        stats = db.get_performance_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print("\n✅ API embedding test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Error testing API embedding: {e}")
        return False
    finally:
        # Restore original environment variables
        if original_url is not None:
            os.environ['EMBEDDING_API_URL'] = original_url
        elif 'EMBEDDING_API_URL' in os.environ:
            del os.environ['EMBEDDING_API_URL']

        if original_key is not None:
            os.environ['EMBEDDING_API_KEY'] = original_key
        elif 'EMBEDDING_API_KEY' in os.environ:
            del os.environ['EMBEDDING_API_KEY']

        if original_model is not None:
            os.environ['EMBEDDING_API_MODEL'] = original_model
        elif 'EMBEDDING_API_MODEL' in os.environ:
            del os.environ['EMBEDDING_API_MODEL']

def test_both_methods():
    """Test và so sánh cả 2 phương pháp"""
    print("\n" + "=" * 60)
    print("🔄 COMPARING LOCAL vs API EMBEDDING")
    print("=" * 60)

    test_text = "So sánh kết quả embedding giữa local và API"

    # Test local
    print("\n🏠 Local embedding result:")
    db_local = TypesenseVectorDB(
        collection_name="compare_local",
        embedding_method="local"
    )
    local_embedding = db_local.generate_embedding(test_text)
    print(f"Dimension: {len(local_embedding)}")
    print(f"First 5 values: {local_embedding[:5]}")

    # Set mock API environment for comparison
    original_url = os.environ.get('EMBEDDING_API_URL')
    original_key = os.environ.get('EMBEDDING_API_KEY')
    original_model = os.environ.get('EMBEDDING_API_MODEL')

    try:
        # Set mock API environment
        os.environ['EMBEDDING_API_URL'] = 'http://localhost:8899/embeddings'
        os.environ['EMBEDDING_API_KEY'] = 'mock_api_key'
        os.environ['EMBEDDING_API_MODEL'] = 'mock-embedding-model'

        # Test API
        print("\n🌐 API embedding result:")
        db_api = TypesenseVectorDB(
            collection_name="compare_api",
            embedding_method="api"
        )
        api_embedding = db_api.generate_embedding(test_text)
        print(f"Dimension: {len(api_embedding)}")
        print(f"First 5 values: {api_embedding[:5]}")

        # So sánh
        print(f"\n📊 Comparison:")
        print(f"Local method: {db_local.embedding_method}")
        print(f"API method: {db_api.embedding_method}")
        print(f"Same dimensions: {len(local_embedding) == len(api_embedding)}")

        # Tính cosine similarity (đơn giản)
        import math
        def cosine_similarity(a, b):
            dot_product = sum(x * y for x, y in zip(a, b))
            magnitude_a = math.sqrt(sum(x * x for x in a))
            magnitude_b = math.sqrt(sum(x * x for x in b))
            return dot_product / (magnitude_a * magnitude_b)

        similarity = cosine_similarity(local_embedding, api_embedding)
        print(f"Cosine similarity: {similarity:.4f}")

    finally:
        # Restore original environment variables
        if original_url is not None:
            os.environ['EMBEDDING_API_URL'] = original_url
        elif 'EMBEDDING_API_URL' in os.environ:
            del os.environ['EMBEDDING_API_URL']

        if original_key is not None:
            os.environ['EMBEDDING_API_KEY'] = original_key
        elif 'EMBEDDING_API_KEY' in os.environ:
            del os.environ['EMBEDDING_API_KEY']

        if original_model is not None:
            os.environ['EMBEDDING_API_MODEL'] = original_model
        elif 'EMBEDDING_API_MODEL' in os.environ:
            del os.environ['EMBEDDING_API_MODEL']

def main():
    """Main function"""
    print("🚀 API EMBEDDING TEST WITH MOCK SERVER")
    
    # Khởi động mock server trong background thread
    server_thread = threading.Thread(target=start_mock_server, daemon=True)
    server_thread.start()
    
    # Đợi server khởi động
    time.sleep(2)
    
    # Test API embedding
    success = test_api_embedding()
    
    if success:
        # Test so sánh cả 2 phương pháp
        test_both_methods()
    
    print("\n" + "=" * 60)
    print("✅ ALL TESTS COMPLETED")
    print("=" * 60)
    print("💡 Tip: Bạn có thể thay đổi EMBEDDING_API_URL trong .env để test với API thật")

if __name__ == "__main__":
    main()
