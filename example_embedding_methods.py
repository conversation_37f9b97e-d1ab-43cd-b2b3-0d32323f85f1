#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Example script demonstrating both local and API embedding methods
"""

import os
from libs.typesense_vector_db import TypesenseVectorDB

def demo_local_embedding():
    """Demo sử dụng embedding local với sentence_transformers"""
    print("=" * 60)
    print("🏠 DEMO LOCAL EMBEDDING (sentence_transformers)")
    print("=" * 60)
    
    # Khởi tạo với phương pháp local
    db = TypesenseVectorDB(
        collection_name="demo_local",
        embedding_method="local"
    )
    
    # Test single text embedding
    print("\n📝 Testing single text embedding...")
    text = "Xin chào, đây là một văn bản tiếng Việt để test embedding."
    embedding = db.generate_embedding(text)
    print(f"Text: {text}")
    print(f"Embedding dimension: {len(embedding)}")
    print(f"First 5 values: {embedding[:5]}")
    
    # Test batch embedding
    print("\n📝 Testing batch embedding...")
    texts = [
        "Câu hỏi đầu tiên về AI",
        "Câu hỏi thứ hai về machine learning", 
        "Câu hỏi thứ ba về deep learning"
    ]
    embeddings = db.generate_embedding(texts, show_progress=True)
    print(f"Generated {len(embeddings)} embeddings")
    for i, emb in enumerate(embeddings):
        print(f"  Text {i+1}: dimension {len(emb)}")
    
    # Performance stats
    print("\n📊 Performance stats:")
    stats = db.get_performance_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    return db

def demo_api_embedding():
    """Demo sử dụng embedding API"""
    print("\n" + "=" * 60)
    print("🌐 DEMO API EMBEDDING")
    print("=" * 60)
    
    # Kiểm tra environment variables
    api_url = os.getenv('EMBEDDING_API_URL')
    if not api_url:
        print("⚠️  EMBEDDING_API_URL not set. Skipping API demo.")
        print("   Set the following environment variables to test API embedding:")
        print("   - EMBEDDING_API_URL: URL của API embedding")
        print("   - EMBEDDING_API_KEY: API key (optional)")
        print("   - EMBEDDING_API_MODEL: Model name (optional)")
        return None
    
    try:
        # Khởi tạo với phương pháp API
        db = TypesenseVectorDB(
            collection_name="demo_api",
            embedding_method="api"
        )
        
        # Test single text embedding
        print("\n📝 Testing single text embedding...")
        text = "Xin chào, đây là một văn bản tiếng Việt để test embedding qua API."
        embedding = db.generate_embedding(text)
        print(f"Text: {text}")
        print(f"Embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
        
        # Test batch embedding
        print("\n📝 Testing batch embedding...")
        texts = [
            "API embedding câu hỏi đầu tiên",
            "API embedding câu hỏi thứ hai", 
            "API embedding câu hỏi thứ ba"
        ]
        embeddings = db.generate_embedding(texts, show_progress=True)
        print(f"Generated {len(embeddings)} embeddings")
        for i, emb in enumerate(embeddings):
            print(f"  Text {i+1}: dimension {len(emb)}")
        
        # Performance stats
        print("\n📊 Performance stats:")
        stats = db.get_performance_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        return db
        
    except Exception as e:
        print(f"❌ Error with API embedding: {e}")
        return None

def demo_search_comparison():
    """Demo so sánh kết quả search giữa 2 phương pháp"""
    print("\n" + "=" * 60)
    print("🔍 DEMO SEARCH COMPARISON")
    print("=" * 60)
    
    # Tạo sample data
    sample_docs = [
        "Artificial Intelligence là gì? AI là khả năng của máy tính để thực hiện các tác vụ thông minh.",
        "Machine Learning là một nhánh của AI, cho phép máy tính học từ dữ liệu.",
        "Deep Learning sử dụng neural networks với nhiều layers để học patterns phức tạp.",
        "Natural Language Processing giúp máy tính hiểu và xử lý ngôn ngữ tự nhiên.",
        "Computer Vision cho phép máy tính nhận diện và phân tích hình ảnh."
    ]
    
    # Test với local embedding
    print("\n🏠 Testing with LOCAL embedding...")
    try:
        db_local = TypesenseVectorDB(
            collection_name="search_test_local",
            embedding_method="local"
        )
        
        # Import sample data
        for i, doc in enumerate(sample_docs):
            # Tạo fake document để test
            result = db_local.client.collections[db_local.collection_name].documents.upsert({
                'id': f'doc_{i}',
                'content': doc,
                'title': f'Document {i+1}',
                'source_file': 'sample.txt',
                'chunk_index': i,
                'metadata_json': '{}',
                'embedding': db_local.generate_embedding(doc),
                'created_at': 1234567890,
                'content_hash': f'hash_{i}'
            })
        
        # Search test
        query = "AI và machine learning là gì?"
        results = db_local.search_similar_documents(query, limit=3)
        print(f"Query: {query}")
        print(f"Found {results['total_found']} documents")
        for doc in results['documents']:
            print(f"  - Similarity: {doc['similarity']:.3f} | {doc['content'][:50]}...")
            
    except Exception as e:
        print(f"❌ Error with local search: {e}")
    
    # Test với API embedding (nếu có)
    api_url = os.getenv('EMBEDDING_API_URL')
    if api_url:
        print("\n🌐 Testing with API embedding...")
        try:
            db_api = TypesenseVectorDB(
                collection_name="search_test_api",
                embedding_method="api"
            )
            
            # Import sample data
            for i, doc in enumerate(sample_docs):
                result = db_api.client.collections[db_api.collection_name].documents.upsert({
                    'id': f'doc_{i}',
                    'content': doc,
                    'title': f'Document {i+1}',
                    'source_file': 'sample.txt',
                    'chunk_index': i,
                    'metadata_json': '{}',
                    'embedding': db_api.generate_embedding(doc),
                    'created_at': 1234567890,
                    'content_hash': f'hash_{i}'
                })
            
            # Search test
            query = "AI và machine learning là gì?"
            results = db_api.search_similar_documents(query, limit=3)
            print(f"Query: {query}")
            print(f"Found {results['total_found']} documents")
            for doc in results['documents']:
                print(f"  - Similarity: {doc['similarity']:.3f} | {doc['content'][:50]}...")
                
        except Exception as e:
            print(f"❌ Error with API search: {e}")
    else:
        print("\n⚠️  Skipping API search test (EMBEDDING_API_URL not set)")

def main():
    """Main function để chạy tất cả demos"""
    print("🚀 EMBEDDING METHODS DEMO")
    print("Testing both local (sentence_transformers) and API embedding methods")
    
    # Demo local embedding
    db_local = demo_local_embedding()
    
    # Demo API embedding
    db_api = demo_api_embedding()
    
    # Demo search comparison
    demo_search_comparison()
    
    print("\n" + "=" * 60)
    print("✅ DEMO COMPLETED")
    print("=" * 60)
    
    # Cleanup caches
    if db_local:
        db_local.clear_embedding_cache()
    if db_api:
        db_api.clear_embedding_cache()

if __name__ == "__main__":
    main()
