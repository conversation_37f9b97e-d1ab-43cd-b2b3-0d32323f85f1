# Hướng dẫn sử dụng 2 phương pháp Embedding

T<PERSON><PERSON> viện `TypesenseVectorDB` hiện hỗ trợ 2 phương pháp tạo embedding:

1. **Local Embedding** - Sử dụng `sentence_transformers` (mặc định)
2. **API Embedding** - Gọi API embedding external

## 1. Local Embedding (sentence_transformers)

### Cấu hình Environment Variables

```bash
# Model embedding (optional, default: all-MiniLM-L6-v2)
EMBEDDINGS_MODEL=all-MiniLM-L6-v2

# Cache size (optional, default: 2000)
EMBEDDING_CACHE_SIZE=2000
```

### Sử dụng

```python
from libs.typesense_vector_db import TypesenseVectorDB

# Khởi tạo với local embedding (mặc định)
db = TypesenseVectorDB(
    collection_name="my_collection",
    embedding_method="local"  # hoặc bỏ qua vì đây là mặc định
)

# Tạo embedding
text = "<PERSON>ă<PERSON> bản cần tạo embedding"
embedding = db.generate_embedding(text)

# Batch embedding
texts = ["Text 1", "Text 2", "Text 3"]
embeddings = db.generate_embedding(texts, show_progress=True)
```

### Ưu điểm
- Không cần internet connection
- Tốc độ nhanh sau khi load model
- Không có giới hạn API calls
- Dữ liệu không rời khỏi máy local

### Nhược điểm
- Cần RAM để load model
- Tốc độ chậm lần đầu load model
- Giới hạn bởi hardware local

## 2. API Embedding

### Cấu hình Environment Variables

```bash
# URL của API embedding (bắt buộc)
EMBEDDING_API_URL=https://api.example.com/v1/embeddings

# API key (optional)
EMBEDDING_API_KEY=your_api_key_here

# Model name (optional)
EMBEDDING_API_MODEL=text-embedding-ada-002

# Cache size (optional, default: 2000)
EMBEDDING_CACHE_SIZE=2000
```

### Sử dụng

```python
from libs.typesense_vector_db import TypesenseVectorDB

# Khởi tạo với API embedding
db = TypesenseVectorDB(
    collection_name="my_collection",
    embedding_method="api"
)

# Tạo embedding
text = "Văn bản cần tạo embedding"
embedding = db.generate_embedding(text)

# Batch embedding
texts = ["Text 1", "Text 2", "Text 3"]
embeddings = db.generate_embedding(texts, show_progress=True)
```

### Format API Expected

API cần hỗ trợ format request như sau:

```json
{
  "input": ["text1", "text2", "text3"],
  "model": "model_name"  // optional
}
```

Response format (OpenAI-style):

```json
{
  "data": [
    {"embedding": [0.1, 0.2, 0.3, ...]},
    {"embedding": [0.4, 0.5, 0.6, ...]},
    {"embedding": [0.7, 0.8, 0.9, ...]}
  ]
}
```

Hoặc format đơn giản:

```json
{
  "embeddings": [
    [0.1, 0.2, 0.3, ...],
    [0.4, 0.5, 0.6, ...],
    [0.7, 0.8, 0.9, ...]
  ]
}
```

### Ưu điểm
- Không cần RAM để load model
- Có thể sử dụng models mạnh hơn
- Không giới hạn bởi hardware local
- Luôn sử dụng model mới nhất

### Nhược điểm
- Cần internet connection
- Có thể có giới hạn API calls
- Latency cao hơn
- Dữ liệu được gửi đến server external

## 3. So sánh Performance

### Kiểm tra performance stats

```python
# Lấy thống kê performance
stats = db.get_performance_stats()
print(stats)

# Output example cho local:
{
    "cache_hits": 150,
    "cache_misses": 50,
    "cache_hit_rate": 0.75,
    "cache_size": 200,
    "cache_maxsize": 2000,
    "embedding_method": "local",
    "embedding_dimension": 384,
    "device": "cuda",
    "model_name": "all-MiniLM-L6-v2"
}

# Output example cho API:
{
    "cache_hits": 100,
    "cache_misses": 30,
    "cache_hit_rate": 0.77,
    "cache_size": 130,
    "cache_maxsize": 2000,
    "embedding_method": "api",
    "embedding_dimension": 384,
    "api_url": "https://api.example.com/v1/embeddings",
    "api_model": "text-embedding-ada-002"
}
```

## 4. Chạy Demo

```bash
# Chạy demo để test cả 2 phương pháp
python example_embedding_methods.py
```

Demo sẽ:
- Test local embedding với sentence_transformers
- Test API embedding (nếu có cấu hình)
- So sánh kết quả search giữa 2 phương pháp
- Hiển thị performance stats

## 5. Lưu ý quan trọng

### Dimension Consistency
- Cả 2 phương pháp đều sử dụng dimension 384 (theo user preference)
- Nếu API trả về dimension khác, sẽ có warning nhưng vẫn hoạt động

### Caching
- Cả 2 phương pháp đều sử dụng LRU cache để tăng tốc
- Cache key dựa trên nội dung text đã preprocess
- Cache size có thể cấu hình qua `EMBEDDING_CACHE_SIZE`

### Error Handling
- Local: Lỗi khi không load được model hoặc hết RAM
- API: Lỗi network, API key sai, hoặc format response không đúng

### Migration
- Có thể chuyển đổi giữa 2 phương pháp bất kỳ lúc nào
- Dữ liệu đã import vẫn tương thích
- Chỉ cần thay đổi `embedding_method` parameter

## 6. Best Practices

### Khi nào dùng Local Embedding:
- Dữ liệu nhạy cảm, không muốn gửi ra ngoài
- Cần tốc độ cao và ổn định
- Có đủ RAM và GPU
- Không có internet connection ổn định

### Khi nào dùng API Embedding:
- Muốn sử dụng models state-of-the-art
- Hardware local hạn chế
- Cần scale lớn
- Không muốn quản lý model infrastructure

### Hybrid Approach:
- Có thể sử dụng cả 2 cho các collections khác nhau
- Local cho dữ liệu nhạy cảm, API cho dữ liệu public
- Fallback từ API sang local khi có lỗi network
