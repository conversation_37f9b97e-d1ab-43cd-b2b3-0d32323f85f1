#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test file for optimized SentenceTransformer implementation
Tests performance improvements, caching, batch processing, and accuracy
"""

import os
import sys
import time
import json
from typing import List, Dict, Any
from libs.typesense_vector_db import TypesenseVectorDB, get_cache_stats, clear_caches

def print_separator(title: str):
    """Print separator with title"""
    print("\n" + "="*80)
    print(f" {title} ")
    print("="*80)

def test_device_optimization():
    """Test device detection and optimization"""
    print_separator("DEVICE OPTIMIZATION TEST")
    
    try:
        db = TypesenseVectorDB(collection_name="test_optimized_embeddings")
        print(f"✅ Device detected: {db.device}")
        print(f"📏 Embedding dimension: {db.embedding_dimension}")
        print(f"🗄️  Cache size: {db.cache_size}")
        return db
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def test_single_vs_batch_performance(db: TypesenseVectorDB):
    """Test performance difference between single and batch processing"""
    print_separator("SINGLE VS BATCH PERFORMANCE TEST")
    
    # Test data
    test_texts = [
        "Đây là câu tiếng Việt đầu tiên để test embedding.",
        "This is an English sentence for testing embeddings.",
        "Câu thứ ba này dài hơn một chút để kiểm tra hiệu suất xử lý văn bản dài hơn.",
        "Fourth sentence with mixed content: tiếng Việt và English.",
        "Câu cuối cùng để hoàn thành bộ test với nội dung đa dạng."
    ] * 10  # 50 texts total
    
    print(f"Testing with {len(test_texts)} texts...")
    
    # Test single processing
    print("\n🔄 Testing single text processing...")
    start_time = time.time()
    single_results = []
    for text in test_texts:
        embedding = db.generate_embedding(text)
        single_results.append(embedding)
    single_time = time.time() - start_time
    
    # Clear cache for fair comparison
    db.clear_embedding_cache()
    
    # Test batch processing
    print("\n🚀 Testing batch processing...")
    start_time = time.time()
    batch_results = db.generate_embedding(test_texts, show_progress=True)
    batch_time = time.time() - start_time
    
    # Compare results
    print(f"\n📊 Performance Comparison:")
    print(f"   Single processing: {single_time:.3f}s ({len(test_texts)/single_time:.1f} texts/sec)")
    print(f"   Batch processing:  {batch_time:.3f}s ({len(test_texts)/batch_time:.1f} texts/sec)")
    print(f"   Speedup: {single_time/batch_time:.1f}x")
    
    # Verify accuracy
    accuracy_ok = True
    for i, (single, batch) in enumerate(zip(single_results, batch_results)):
        if abs(sum(single) - sum(batch)) > 0.001:  # Small tolerance for floating point
            print(f"❌ Accuracy mismatch at index {i}")
            accuracy_ok = False
            break
    
    if accuracy_ok:
        print("✅ Accuracy verified: Single and batch results match")
    
    return single_time, batch_time

def test_caching_performance(db: TypesenseVectorDB):
    """Test embedding caching performance"""
    print_separator("CACHING PERFORMANCE TEST")
    
    # Test texts with duplicates
    test_texts = [
        "Repeated text for cache testing",
        "Another text for testing",
        "Repeated text for cache testing",  # Duplicate
        "Third unique text",
        "Another text for testing",  # Duplicate
        "Repeated text for cache testing",  # Duplicate
    ]
    
    print("🧹 Clearing cache...")
    db.clear_embedding_cache()
    
    print(f"Testing with {len(test_texts)} texts (including duplicates)...")
    
    # First run - populate cache
    print("\n🔄 First run (populating cache)...")
    start_time = time.time()
    results1 = db.generate_embedding(test_texts, show_progress=True)
    first_run_time = time.time() - start_time
    
    # Second run - should use cache
    print("\n♻️  Second run (using cache)...")
    start_time = time.time()
    results2 = db.generate_embedding(test_texts, show_progress=True)
    second_run_time = time.time() - start_time
    
    # Get cache stats
    stats = db.get_performance_stats()
    
    print(f"\n📊 Caching Performance:")
    print(f"   First run:  {first_run_time:.3f}s")
    print(f"   Second run: {second_run_time:.3f}s")
    print(f"   Speedup: {first_run_time/second_run_time:.1f}x")
    print(f"   Cache hit rate: {stats['cache_hit_rate']:.1%}")
    print(f"   Cache size: {stats['cache_size']}/{stats['cache_maxsize']}")
    
    return stats

def test_text_preprocessing(db: TypesenseVectorDB):
    """Test text preprocessing functionality"""
    print_separator("TEXT PREPROCESSING TEST")
    
    # Test various text conditions
    test_cases = [
        ("Normal text", "This is normal text"),
        ("Extra whitespace", "This   has    extra     whitespace"),
        ("Leading/trailing spaces", "   Text with spaces   "),
        ("Empty text", ""),
        ("Very long text", "A" * 10000),  # Very long text
        ("Mixed content", "Tiếng Việt mixed with English and 123 numbers!"),
    ]
    
    for name, text in test_cases:
        print(f"\n🧪 Testing: {name}")
        print(f"   Input: '{text[:50]}{'...' if len(text) > 50 else ''}'")
        
        try:
            start_time = time.time()
            embedding = db.generate_embedding(text)
            elapsed = time.time() - start_time
            
            print(f"   Output dimension: {len(embedding)}")
            print(f"   Processing time: {elapsed:.3f}s")
            print(f"   ✅ Success")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_memory_usage():
    """Test memory usage and cache management"""
    print_separator("MEMORY USAGE TEST")
    
    print("📊 Initial cache stats:")
    initial_stats = get_cache_stats()
    print(json.dumps(initial_stats, indent=2))
    
    # Create multiple instances to test model caching
    print("\n🔄 Creating multiple TypesenseVectorDB instances...")
    dbs = []
    for i in range(3):
        db = TypesenseVectorDB(collection_name=f"test_memory_{i}")
        dbs.append(db)
        print(f"   Instance {i+1} created")
    
    print("\n📊 Cache stats after creating instances:")
    final_stats = get_cache_stats()
    print(json.dumps(final_stats, indent=2))
    
    # Test cache clearing
    print("\n🧹 Clearing all caches...")
    clear_caches()
    for db in dbs:
        db.clear_embedding_cache()
    
    print("\n📊 Cache stats after clearing:")
    cleared_stats = get_cache_stats()
    print(json.dumps(cleared_stats, indent=2))

def main():
    """Main test function"""
    print_separator("OPTIMIZED SENTENCE TRANSFORMER TESTS")
    print("🧪 Testing all optimization features...")
    
    # Test 1: Device optimization
    db = test_device_optimization()
    if not db:
        print("❌ Cannot proceed without database connection")
        return
    
    # Test 2: Performance comparison
    single_time, batch_time = test_single_vs_batch_performance(db)
    
    # Test 3: Caching performance
    cache_stats = test_caching_performance(db)
    
    # Test 4: Text preprocessing
    test_text_preprocessing(db)
    
    # Test 5: Memory usage
    test_memory_usage()
    
    # Final summary
    print_separator("TEST SUMMARY")
    print("✅ All optimization tests completed!")
    print(f"🚀 Batch processing speedup: {single_time/batch_time:.1f}x")
    print(f"♻️  Cache hit rate: {cache_stats['cache_hit_rate']:.1%}")
    print(f"🖥️  Device: {db.device}")
    print(f"📏 Embedding dimension: {db.embedding_dimension}")
    
    print("\n🎯 Optimization Benefits:")
    print("   ✅ Batch processing for faster imports")
    print("   ✅ LRU caching for duplicate text handling")
    print("   ✅ Automatic device optimization (GPU/MPS/CPU)")
    print("   ✅ Model caching to avoid reloading")
    print("   ✅ Text preprocessing for better quality")
    print("   ✅ Performance monitoring and statistics")

if __name__ == "__main__":
    main()
