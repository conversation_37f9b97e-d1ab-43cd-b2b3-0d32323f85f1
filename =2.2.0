Collecting sentence-transformers
  Downloading sentence_transformers-5.0.0-py3-none-any.whl.metadata (16 kB)
Collecting transformers<5.0.0,>=4.41.0 (from sentence-transformers)
  Downloading transformers-4.53.1-py3-none-any.whl.metadata (40 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 40.9/40.9 kB 640.0 kB/s eta 0:00:00
Requirement already satisfied: tqdm in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from sentence-transformers) (4.67.1)
Collecting torch>=1.11.0 (from sentence-transformers)
  Downloading torch-2.7.1-cp311-none-macosx_11_0_arm64.whl.metadata (29 kB)
Collecting scikit-learn (from sentence-transformers)
  Downloading scikit_learn-1.7.0-cp311-cp311-macosx_12_0_arm64.whl.metadata (31 kB)
Collecting scipy (from sentence-transformers)
  Downloading scipy-1.16.0-cp311-cp311-macosx_12_0_arm64.whl.metadata (61 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 61.9/61.9 kB 3.0 MB/s eta 0:00:00
Requirement already satisfied: huggingface-hub>=0.20.0 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from sentence-transformers) (0.33.1)
Collecting Pillow (from sentence-transformers)
  Downloading pillow-11.3.0-cp311-cp311-macosx_11_0_arm64.whl.metadata (9.0 kB)
Requirement already satisfied: typing_extensions>=4.5.0 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from sentence-transformers) (4.14.0)
Requirement already satisfied: filelock in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (3.18.0)
Requirement already satisfied: fsspec>=2023.5.0 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2025.5.1)
Requirement already satisfied: packaging>=20.9 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (24.2)
Requirement already satisfied: pyyaml>=5.1 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (6.0.2)
Requirement already satisfied: requests in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2.32.4)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (1.1.5)
Collecting sympy>=1.13.3 (from torch>=1.11.0->sentence-transformers)
  Using cached sympy-1.14.0-py3-none-any.whl.metadata (12 kB)
Collecting networkx (from torch>=1.11.0->sentence-transformers)
  Downloading networkx-3.5-py3-none-any.whl.metadata (6.3 kB)
Collecting jinja2 (from torch>=1.11.0->sentence-transformers)
  Using cached jinja2-3.1.6-py3-none-any.whl.metadata (2.9 kB)
Requirement already satisfied: numpy>=1.17 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.3.1)
Requirement already satisfied: regex!=2019.12.17 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2024.11.6)
Collecting tokenizers<0.22,>=0.21 (from transformers<5.0.0,>=4.41.0->sentence-transformers)
  Downloading tokenizers-0.21.2-cp39-abi3-macosx_11_0_arm64.whl.metadata (6.8 kB)
Collecting safetensors>=0.4.3 (from transformers<5.0.0,>=4.41.0->sentence-transformers)
  Using cached safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl.metadata (3.8 kB)
Collecting joblib>=1.2.0 (from scikit-learn->sentence-transformers)
  Downloading joblib-1.5.1-py3-none-any.whl.metadata (5.6 kB)
Collecting threadpoolctl>=3.1.0 (from scikit-learn->sentence-transformers)
  Using cached threadpoolctl-3.6.0-py3-none-any.whl.metadata (13 kB)
Collecting mpmath<1.4,>=1.1.0 (from sympy>=1.13.3->torch>=1.11.0->sentence-transformers)
  Using cached mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)
Collecting MarkupSafe>=2.0 (from jinja2->torch>=1.11.0->sentence-transformers)
  Using cached MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl.metadata (4.0 kB)
Requirement already satisfied: charset_normalizer<4,>=2 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.4.2)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2.5.0)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/.pyenv/versions/3.11.12/envs/ai-test/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2025.6.15)
Downloading sentence_transformers-5.0.0-py3-none-any.whl (470 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 470.2/470.2 kB 4.1 MB/s eta 0:00:00
Downloading torch-2.7.1-cp311-none-macosx_11_0_arm64.whl (68.6 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 68.6/68.6 MB 5.3 MB/s eta 0:00:00
Downloading transformers-4.53.1-py3-none-any.whl (10.8 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.8/10.8 MB 2.9 MB/s eta 0:00:00
Downloading pillow-11.3.0-cp311-cp311-macosx_11_0_arm64.whl (4.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 4.7/4.7 MB 2.5 MB/s eta 0:00:00
Downloading scikit_learn-1.7.0-cp311-cp311-macosx_12_0_arm64.whl (10.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 10.7/10.7 MB 2.9 MB/s eta 0:00:00
Downloading scipy-1.16.0-cp311-cp311-macosx_12_0_arm64.whl (28.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 28.5/28.5 MB 1.6 MB/s eta 0:00:00
Downloading joblib-1.5.1-py3-none-any.whl (307 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 307.7/307.7 kB 1.1 MB/s eta 0:00:00
Using cached safetensors-0.5.3-cp38-abi3-macosx_11_0_arm64.whl (418 kB)
Using cached sympy-1.14.0-py3-none-any.whl (6.3 MB)
Using cached threadpoolctl-3.6.0-py3-none-any.whl (18 kB)
Downloading tokenizers-0.21.2-cp39-abi3-macosx_11_0_arm64.whl (2.7 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.7/2.7 MB 1.7 MB/s eta 0:00:00
Using cached jinja2-3.1.6-py3-none-any.whl (134 kB)
Downloading networkx-3.5-py3-none-any.whl (2.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.0/2.0 MB 2.1 MB/s eta 0:00:00
Using cached MarkupSafe-3.0.2-cp311-cp311-macosx_11_0_arm64.whl (12 kB)
Using cached mpmath-1.3.0-py3-none-any.whl (536 kB)
Installing collected packages: mpmath, threadpoolctl, sympy, scipy, safetensors, Pillow, networkx, MarkupSafe, joblib, scikit-learn, jinja2, torch, tokenizers, transformers, sentence-transformers
Successfully installed MarkupSafe-3.0.2 Pillow-11.3.0 jinja2-3.1.6 joblib-1.5.1 mpmath-1.3.0 networkx-3.5 safetensors-0.5.3 scikit-learn-1.7.0 scipy-1.16.0 sentence-transformers-5.0.0 sympy-1.14.0 threadpoolctl-3.6.0 tokenizers-0.21.2 torch-2.7.1 transformers-4.53.1
