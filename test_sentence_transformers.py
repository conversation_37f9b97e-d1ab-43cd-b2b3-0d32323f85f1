#!/usr/bin/env python3
"""
Test script để kiểm tra việc chuyển đổi từ OpenAIEmbeddings sang sentence_transformers
trong TypesenseVectorDB
"""

import os
import sys
from libs.typesense_vector_db import TypesenseVectorDB

def test_sentence_transformers_integration():
    """Test việc tích hợp sentence_transformers"""
    print("🧪 Testing sentence_transformers integration...")
    
    try:
        # Khởi tạo TypesenseVectorDB
        print("\n1. Khởi tạo TypesenseVectorDB...")
        db = TypesenseVectorDB(collection_name="test_sentence_transformers")
        
        # Kiểm tra model đã được load
        print(f"✅ Model loaded: {db.embeddings}")
        print(f"📏 Embedding dimension: {db.embedding_dimension}")
        
        # Test tạo embedding
        print("\n2. Testing embedding generation...")
        test_texts = [
            "Xin chào, tôi là một câu tiếng Vi<PERSON>t.",
            "Hello, I am an English sentence.",
            "<PERSON><PERSON><PERSON> là một văn bản dài hơn để kiểm tra khả năng tạo embedding của sentence_transformers với nội dung tiếng Việt."
        ]
        
        for i, text in enumerate(test_texts, 1):
            embedding = db.generate_embedding(text)
            print(f"   Text {i}: {text[:50]}...")
            print(f"   Embedding dimension: {len(embedding)}")
            print(f"   First 5 values: {embedding[:5]}")
            print()
        
        # Test so sánh với embedding tương tự
        print("3. Testing similarity...")
        text1 = "Tôi thích ăn phở"
        text2 = "Tôi yêu thích món phở"
        text3 = "I like programming"
        
        emb1 = db.generate_embedding(text1)
        emb2 = db.generate_embedding(text2)
        emb3 = db.generate_embedding(text3)
        
        # Tính cosine similarity đơn giản
        def cosine_similarity(a, b):
            import math
            dot_product = sum(x * y for x, y in zip(a, b))
            magnitude_a = math.sqrt(sum(x * x for x in a))
            magnitude_b = math.sqrt(sum(x * x for x in b))
            return dot_product / (magnitude_a * magnitude_b)
        
        sim_1_2 = cosine_similarity(emb1, emb2)
        sim_1_3 = cosine_similarity(emb1, emb3)
        
        print(f"   Similarity between '{text1}' and '{text2}': {sim_1_2:.4f}")
        print(f"   Similarity between '{text1}' and '{text3}': {sim_1_3:.4f}")
        print(f"   ✅ Vietnamese texts should be more similar: {sim_1_2 > sim_1_3}")
        
        print("\n✅ All tests passed! sentence_transformers integration successful.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_info():
    """Hiển thị thông tin về model"""
    print("\n📊 Model Information:")
    try:
        from sentence_transformers import SentenceTransformer
        
        model_name = os.getenv('EMBEDDINGS_MODEL', 'all-MiniLM-L6-v2')
        model = SentenceTransformer(model_name)
        
        print(f"   Model name: {model_name}")
        print(f"   Model device: {model.device}")
        print(f"   Max sequence length: {model.max_seq_length}")
        
        # Test embedding dimension
        test_embedding = model.encode("test")
        print(f"   Embedding dimension: {len(test_embedding)}")
        print(f"   Embedding type: {type(test_embedding)}")
        
    except Exception as e:
        print(f"❌ Could not get model info: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print(" SENTENCE TRANSFORMERS INTEGRATION TEST ")
    print("=" * 60)
    
    # Test model info
    test_model_info()
    
    # Test integration
    success = test_sentence_transformers_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ sentence_transformers đã được tích hợp thành công!")
    else:
        print("❌ SOME TESTS FAILED!")
        sys.exit(1)
